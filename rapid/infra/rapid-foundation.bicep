// RAPID Foundation Azure Infrastructure (Bicep Template)

targetScope = 'subscription'

@description('Name of the Resource Group')
param resourceGroupName string = 'rapid-rg'

@description('Location for all resources')
param location string = 'australiaeast'

resource rg 'Microsoft.Resources/resourceGroups@2021-04-01' = {
  name: resourceGroupName
  location: location
}

resource storage 'Microsoft.Storage/storageAccounts@2022-09-01' = {
  name: 'rapidstorage${uniqueString(resourceGroupName)}'
  location: location
  sku: {
    name: 'Standard_LRS'
  }
  kind: 'StorageV2'
  properties: {
    accessTier: 'Hot'
  }
}

resource keyVault 'Microsoft.KeyVault/vaults@2022-07-01' = {
  name: 'rapid-keyvault-${uniqueString(resourceGroupName)}'
  location: location
  properties: {
    tenantId: subscription().tenantId
    sku: {
      family: 'A'
      name: 'standard'
    }
    accessPolicies: []
    enabledForDeployment: true
    enabledForTemplateDeployment: true
    enabledForDiskEncryption: true
  }
}

resource appServicePlan 'Microsoft.Web/serverfarms@2022-03-01' = {
  name: 'rapid-appserviceplan'
  location: location
  sku: {
    name: 'P1v2'
    tier: 'PremiumV2'
    size: 'P1v2'
    capacity: 1
  }
  properties: {}
}

resource backendApp 'Microsoft.Web/sites@2022-03-01' = {
  name: 'rapid-backend-api'
  location: location
  properties: {
    serverFarmId: appServicePlan.id
    httpsOnly: true
  }
}

resource webuiApp 'Microsoft.Web/sites@2022-03-01' = {
  name: 'rapid-webui'
  location: location
  properties: {
    serverFarmId: appServicePlan.id
    httpsOnly: true
  }
}

// NOTE: Azure OpenAI, AI Search, Prompt Flow, Logic Apps, Data Factory, and Functions
// should be provisioned as needed. Add their modules here as required.

// NOTE: Azure AD App Registrations for Web UI, Backend, and VS Code plugin
// must be created manually or via automation (not supported in Bicep as of now).
// Document their client IDs and secrets in Key Vault.

output resourceGroupName string = resourceGroupName
output storageAccountName string = storage.name
output keyVaultName string = keyVault.name
output backendAppName string = backendApp.name
output webuiAppName string = webuiApp.name