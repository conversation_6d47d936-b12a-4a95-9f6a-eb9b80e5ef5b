<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>RAPID Development - Reengineering Development, Doubling Productivity</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: #f7f7f7;
    }
    .container {
      display: flex;
      height: 100vh;
    }
    .sidebar {
      width: 280px;
      background: #222c36;
      color: #fff;
      padding: 0;
      box-shadow: 2px 0 8px rgba(0,0,0,0.05);
      display: flex;
      flex-direction: column;
    }
    .sidebar h2 {
      margin: 0;
      padding: 24px 24px 12px 24px;
      font-size: 1.2em;
      border-bottom: 1px solid #334;
      background: #1a2027;
    }
    .outline {
      list-style: none;
      padding: 0;
      margin: 0;
      flex: 1;
    }
    .outline li {
      padding: 16px 24px;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background 0.2s, border-color 0.2s;
    }
    .outline li.active, .outline li:hover {
      background: #2d3a4a;
      border-left: 4px solid #4fc3f7;
    }
    .tree ul {
      display: block;
    }
    .tree-node > ul {
      display: block;
    }
    .tree-node:not(.expanded) > ul {
      display: none;
    }
    .main {
      flex: 1;
      padding: 0 64px 48px 64px;
      background: #fff;
      overflow-y: auto;
      min-width: 0;
      box-sizing: border-box;
      font-size: 1.25em;
    }
    .main-header {
      width: 100%;
      background: #003366;
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 32px;
      padding: 0;
    }
    .main-header img {
      height: 100%;
      margin-left: 12px;
      object-fit: contain;
    }
    .slide {
      display: none;
      animation: fadeIn 0.4s;
    }
    .slide.active {
      display: block;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .roles-table {
      width: 100%;
      border-collapse: collapse;
      margin: 24px 0;
    }
    .roles-table th, .roles-table td {
      font-size: 0.75em;
      border: 1px solid #bbb;
      padding: 8px 12px;
      text-align: left;
    }
    .roles-table th {
      background: #e3f2fd;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }

    /* Remove bullets/circles from all nested menu levels in sidebar */
    .tree ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    /* Remove bullets/circles from all nested menu levels in sidebar */
    .tree ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }

  </style>
</head>
<body>
  <div class="container">
    <nav class="sidebar">
      <h2>Outline</h2>
      <ul class="outline tree" id="outline">
        <li class="tree-node expanded active" data-section="section1">
          <span class="tree-toggle">▶</span> Executive Summary
          <ul>
            <li class="tree-leaf" data-section="section1.1">Problem Statement</li>
            <li class="tree-leaf" data-section="section1.2">Solution Outcome</li>
            <li class="tree-leaf" data-section="section1.3">Benchmarking Progress to Date</li>
            <li class="tree-leaf" data-section="section1.4">Solution: Agile-Waterfall to Agile-RAPID</li>
          </ul>
        </li>
        <li class="tree-node" data-section="section2">
          <span class="tree-toggle">▶</span> Rapid Design
          <ul>
            <li class="tree-leaf" data-section="section2.1">Problem Statement</li>
            <li class="tree-leaf" data-section="section2.2">Current Defined Process</li>
            <li class="tree-leaf" data-section="section2.3">Actual Process</li>
            <li class="tree-leaf" data-section="section2.4">Process Reengineering using RAPID</li>
            <li class="tree-leaf" data-section="section2.5">Solution: RAPID Design Agent</li>
            <li class="tree-leaf" data-section="section2.6">Benefit</li>
            <li class="tree-leaf" data-section="section2.7">Bench Mark</li>
          </ul>
        </li>
        <li class="tree-node" data-section="section3">
          <span class="tree-toggle">▶</span> Rapid API Initializr
          <ul>
            <li class="tree-leaf" data-section="section3.1">Problem Statement</li>
            <li class="tree-leaf" data-section="section3.2">Solution: RAPID API Initializr</li>
            <li class="tree-leaf" data-section="section3.3">Bench Mark</li>
          </ul>
        </li>
      </ul>
    </nav>
    <main class="main">
      <div class="main-header">
        <img src="images/westpac-logo.png" alt="Westpac Logo">
      </div>
      <section class="slide section-content active" id="section1">
        <h1>RAPID Development - Reengineering Development, Doubling Productivity</h1>
        <ul>
          <li><b>RAPID Methodology</b>
            <ul>
              <li>Agile squad model</li>
              <li>Reduced informamtion passing</li>
              <li>Feature based end to end delivery</li>
              <li>AI code generation</li>
            </ul>
          </li>
          <li><b>RAPID AI product portfolio</b>
            <ul>
              <li>RAPID Designer</li>
              <li>RAPID API Initializr</li>
              <li>RAPID MFE Initializr</li>
              <li>RAPID Code</li>
            </ul>
          </li>
        </ul>
        <img src="images/rapid-design.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
        <img src="images/rapid-initializr.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
        <img src="images/rapid-code.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section1.1">
        <h2>Problem Statement</h2>
        <ul>
          <li>High manual effort</li>
          <li>Expensive to control the quality and standard
            <ul>
              <li>Overly complex workflow</li>
              <li>Missing unit test</li>
            </ul>
          </li>
          <li>Long wait times due to human dependencies
            <ul>
              <li>Waiting couple days is usual</li>
            </ul>
          </li>
          <li>No business value delivered in early stages
            <ul>
              <li>Up to 2 months</li>
            </ul>
          </li>
          <li>Overheads of boilerplate work</li>
        </ul>
        <h2>It's taking weeks to months to finalize the API contract</h2>
        <ul>
          <li>It's taking weeks to months to finalize the API contract (swagger / openapi)</li>
          <li>Refer to these Jira tickets:
            <ul>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-32">CTPDCF-32</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-33">CTPDCF-33</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-94">CTPDCF-94</a></li>
            </ul>
          </li>
        </ul>
      </section>
      <section class="slide section-content" id="section1.2">
        <h2>Solution Outcome with RAPID Development</h2>
        <ul>
          <li><b>Productivity improvement</b>
            <ul>
              <li>Reduces setup time from 2 months to days</li>
              <li>Frees up developers for business logic</li>
              <li>Reduced repetitive boilerplate work</li>
              <li>Increase business value through significant improvement in BCE</li>
            </ul>
          </li>
          <li><b>Better quality</b>
            <ul>
              <li>Built-in mesh compliance and best practices by default</li>
              <li>Better unit test coverage</li>
            </ul>
          </li>
          <li><b>Cost reduction</b>
            <ul>
              <li>Supporting development process improvement to achieve 50% off the current cost by doubling the production</li>
            </ul>
          </li>
        </ul>
      </section>
      <section class="slide section-content" id="section1.4">
        <h2>Regineering Agile-Waterfall to Agile-RAPID</h2>
        <ul>
          <li><a href="Contemporary-Software-Development-Model.html">Contemporary Software Development Model</a></li>
        </ul>
        <img src="images/communication-net-improvement.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section1.5">
      </section>
      <section class="slide section-content" id="section1.3">
        <h2>Benchmarking Progress to Date</h2>
        <ul>
          <li><b>API contract swagger design - Current approach vs RAPID:</b>
            <ul>
              <li>From 20+ days to 1 day</li>
            </ul>
          </li>
          <li><b>Code base scaffolding - Current approach vs RAPID:</b>
            <ul>
              <li>From 3 days to 3 hours</li>
            </ul>
          </li>
          <li><b>Full development cycle - Current approach vs RAPID:</b>
            <ul>
              <li>Expected to be doubling the productivity (to be benchmarked)</li>
            </ul>
          </li>
        </ul>
      </section>
      <section class="slide section-content" id="section2.1">
        <h2>It's taking weeks to months to finalize the API contract</h2>
        <ul>
          <li>It's taking weeks to months to finalize the API contract (swagger / openapi)</li>
          <li>Refer to these Jira tickets:
            <ul>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-32">CTPDCF-32</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-33">CTPDCF-33</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-94">CTPDCF-94</a></li>
            </ul>
          </li>
        </ul>
        <img src="images/swagger-design-ticket.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section2.2">
        <h3>Current Defined Process</h3>
        <ul>
          <li>Solution designer define the API contract in human language</li>
          <li>Developer translate into the swagger/openapi yaml</li>
          <li>Submit to solution designer to review</li>
          <li>Solution designer endorse</li>
          <li>The finalized swagger/openapi yaml</li>
        </ul>
        <img src="images/defined-process.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section2.3">
        <h2>Actual Process</h2>
        <ul>
          <li>Solution designer define the API contract in human language</li>
          <li>Developer translate into the swagger/openapi yaml</li>
          <li>Submit to solution designer to review</li>
          <li>Wait couple days</li>
          <li>Solution designer review and leave comment</li>
          <li>Developer update the swagger/openapi yaml and submit to review again</li>
          <li>   </li>
          <li>Wait couple days</li>
          <li>Solution designer review and leave comment</li>
          <li>Developer update the swagger/openapi yaml and submit to review again</li>
          <li>   </li>
          <li>Wait couple days</li>
          <li>Solution designer review and leave comment</li>
          <li>Developer update the swagger/openapi yaml and submit to review again</li>
          <li>   </li>

          <li>(Repeat above steps 5-10 times)</li>
          <li>Solution designer endorse</li>
          <li>The finalized swagger/openapi yaml</li>
        </ul>
        <p>Eventually after 1-2 months we get the finalized API contract</p>
        <img src="images/actual-proces.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section2.4">
        <h2>Process Reengineering using RAPID</h2>
        <ul>
          <li>Solution designer to own the swagger development</li>
          <li>Solution designer define and provide the swagger yaml</li>
          <li><b>Challenge without RAPID:</b> Writing the swagger/openapi yaml is time consuming. We have limited solution designers.</li>
        </ul>
        <img src="images/improved-process.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section2.5">
        <h2>Solution: RAPID Design Agent</h2>
        <ul>
          <li>Writing the swagger/openapi yaml is time consuming and we have limited solution designers.</li>
          <li>We want to best utilize the solution designer's time. That's the reason why we are adopting the current process of design-translate-endorse process.</li>
          <li>If a solution designer can write a swagger yaml easily, we could have solution designer write the swagger directly and make it part of the solution design.</li>
          <li>We have developed the AI powered design assistant RAPID (RAG Agentic Programmer Interactive Development) and it provides a swagger editor.</li>
        </ul>
        <img src="images/rapid-design.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section2.6">
        <h2>Benefit</h2>
        <ul>
          <li><b>Higher Productivity:</b> Get the finalized API contract from months to days</li>
          <li><b>Improve the BCE significantly:</b> As the swagger is the prerequisite of starting the development. The earlier the swagger is finalized, the earlier development could start.</li>
          <li><b>Better quality:</b> With less unnecessary information passing, less chance of mistake will happen</li>
        </ul>
        <img src="images/bce-up.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section2.7">
        <h2>RAPID vs Copilot vs Traditional Way of creating the swagger</h2>
        <table class="roles-table">
          <thead>
            <tr>
              <th>Approach</th>
              <th>Time spent</th>
              <th>Pros</th>
              <th>Cons</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                Manual editing
                <li>Solution designer define the API contract in human language. </li>
                <li>Developer create the swagger yaml manually.</li>
              </td>
            <td>
                <li>Solution designer ~2 hours to design each resource. </li>
                <li>Developer ~6 hours for the first resource, </li>
                <li>~2 hours for the second resource. </li>
                <li>Review ~3 days. 10+ days waiting time for the solution designer's availability.</li>
              </td>
              <td></td>
              <td>Slow and painful. Back and forth review</td>
            </tr>
            <tr>
              <td>Writing Java and generate the swagger
                <li>Solution designer define the API contract in human language. </li>
                <li>Developers write Java class first with copilot.</li>
                <li>Generate the swagger by spring. </li>
                <li>Manually modify. </li>
              </td>
              <td>
                <li>Solution designer ~2 hours to design each resource.</li>
                <li>Developer ~3 hours for the first resource.</li>
                <li>~1 hour for the second.</li>
                <li>Review ~3 days.</li>
                <li>10+ days waiting time for the solution designer's availability.</li>
              </td>
              <td>Quicker than manual. </td>
              <td>
                <li>Need to prepare another workspace. </li>
                <li>The developer still need to write class field by field. </li>
                <li>Back and forth review</li>
              </td>
            </tr>
            <tr>
              <td>
                With Copilot
                <li>Solution designer define the API contract in human language.</li>
                <li>Based on a basic template and the API contract in human language.</li>
                <li>Prepare a big prompt to AI (Copilot).</li>
                <li>Copilot to generate. </li>
                <li>Verify by human eyeball and fix by either another prompt or manually.</li>
              </td>
              <td>
                <li>Solution designer ~2 hours to design each resource.</li>
                <li>Developer ~30 mins for each resource.</li>
                <li>Review ~3 days.</li>
                <li>10+ days waiting time for the solution designer's availability.</li>
              </td>
              <td>It takes much less time for developer comparing. </td>
              <td>
                <li>Every developer may have different prompt. </li>
                <li>Solution designer review required. Back and forth review</li>
              </td>
            </tr>
            <tr>
              <td>
                With RAPID
                <li>Solution designer use RAPID to define the API contract. </li>
                <li>RAPID generate the swagger yaml file. </li>
                <li>Verify by human eyeball and fix by either another prompt or manually. </li>
              </td>
              <td>
                40 mins for each resource.
              </td>
              <td>
                <li>Have better control of the standard.</li>
                <li>Save the current back and forth review process which normally counted by 10+ days.</li>
                <li>Will have the final swagger file in couple hours.</li>
              </td>
              <td>
                We need to deploy another system (RAPID)
              </td>
            </tr>
          </tbody>
        </table>
      </section>
      <section class="slide section-content" id="section3.1">
        <h2>Where are we now – scaffolding break down</h2>
        <ul>
          <li><b>Current API Scaffolding Development Workflow – High Effort, Low Value</b>
            <ul>
              <li>Run mesh codegen – Scaffold generation (~30 mins)</li>
              <li>Prepare pom.xml – Mesh standard (~500+ lines, ~4+ hours)</li>
              <li>Run maven command <code>mvn clean install</code> – (~3 mins)</li>
              <li>Write core components and unit test – Controller, Service, Mapper, Client, DB Repo (~1-2 days)</li>
              <li>Check-in & wait for code review – Human dependency (2–3 days delay)</li>
              <li>Tech Lead Review – (~0.5 day)</li>
            </ul>
          </li>
          <li>Before any business logic is written, ~3 developer-days per API spent</li>
        </ul>
      </section>
      <section class="slide section-content" id="section3.2">
        <h2>Where are we going – RAPID Build Agent</h2>
        <ul>
          <li>Automate the scaffold building</li>
          <li>Present the idea
            <ul>
              <li>Select step needed</li>
              <li>Write some prompt</li>
              <li>Write some business logic</li>
              <li>~30 minutes effort</li>
            </ul>
          </li>
          <li>Generate the code scaffold with test cases in 1 minute</li>
        </ul>
      </section>
      <section class="slide section-content" id="section3.3">
        <h2>RAPID Code vs Copilot vs Manual in scaffolding</h2>
        <table class="roles-table" style="margin-bottom: 32px;">
          <thead style="background: #b3aaff;">
            <tr>
              <th>Approach</th>
              <th>Time cost</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <b>Manual</b>
                <ul>
                  <li>Run mesh <a href="#">codegen</a> – Scaffold generation (~30 mins)</li>
                  <li>Prepare pom.xml – Mesh standard (~500+ lines, ~4+ hours)</li>
                  <li>Run <code>mvn clean install</code> – (~3 mins)</li>
                  <li>Write core components and unit test – Controller, Service, Mapper, Client, DB Repo (~1-2 days)</li>
                  <li>Check-in & wait for code review – Human dependency (2–3 days delay)</li>
                  <li>Tech <a href="#">Lead</a> Review – (~0.5 day)</li>
                </ul>
              </td>
              <td>
                3 developer-days effort +<br>
                2-3 days waiting time
              </td>
            </tr>
            <tr>
              <td>
                <b>Copilot</b>
                <ul>
                  <li>Same steps required as above.</li>
                  <li>Saving 50% on writing core components and unit test from 1-2 days to 0.5-1 day.</li>
                </ul>
              </td>
              <td>
                2 developer-days effort +<br>
                2-3 days waiting time
              </td>
            </tr>
            <tr>
              <td>
                <b>RAPID</b>
                <ul>
                  <li>Upload <a href="#">openapi</a> contract, upload <a href="#">openapi</a> contract of <a href="#">downstreams</a></li>
                  <li>Select resources to build.</li>
                  <li>Add predefined common steps</li>
                  <li>Describe idea in human language for each step if required.</li>
                  <li>Click generate and download</li>
                </ul>
              </td>
              <td>
                Within 1 hour
              </td>
            </tr>
          </tbody>
        </table>
      </section>
      <section class="slide section-content" id="section12">
        <h2>RAPID vs Copilot in Business Logic Development</h2>
        <table class="roles-table">
          <thead>
            <tr>
              <th>Approach</th>
              <th>Pros and Cons</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Copilot in IDE</td>
              <td>Can read the codes in same repository. Manual copy business logic from jira/confluence and paste to IDE copilot and generate. Cannot paste too much, the copilot will hang. Do small chunks step by step. Copy-paste the generated code to IDE. Save some manual work on typing the codes and test. Less mistakes. Need to copy-paste the business to IDE. Generate code snippet only. Manually copy to files.</td>
            </tr>
            <tr>
              <td>Copilot in Edge</td>
              <td>Can read Jira/Confluence page. Not knowing the existing codes. Code generated is not Westpac standard compliant. Save some manual work on copy-paste the business logic and typing the codes and test. Need to copy-paste existing code to Edge. Generate code snippet only. Manually copy to files.</td>
            </tr>
            <tr>
              <td>RAPID integrated with Jira and Confluence</td>
              <td>Upload openapi contract, upload openapi contract of downstreams. Select resources to build. Add predefined common steps. Describe idea in human language for each step if required. Click generate and download. Save more time than above. Having both benefit of above. Generate multiple file in one go.</td>
            </tr>
          </tbody>
        </table>
      </section>
      <section class="slide section-content" id="section13">
        <h2>Thank you</h2>
      </section>
    </main>
  </div>
  <script>
    // Tree menu navigation logic with event delegation
    document.addEventListener('DOMContentLoaded', function() {
      const outline = document.getElementById('outline');
      const sections = document.querySelectorAll('.section-content');

      // Make all tree-nodes foldable, like Contemporary-Software-Development-Model.html
      outline.querySelectorAll('.tree-node').forEach(function(node) {
        if (node.querySelector('ul')) {
          // Only expand the first node by default
          if (node === outline.querySelector('.tree-node')) {
            node.classList.add('expanded');
            node.querySelector('.tree-toggle').textContent = '▼';
          } else {
            node.classList.remove('expanded');
            node.querySelector('.tree-toggle').textContent = '▶';
          }
        }
      });


      function showSection(sectionId) {
        sections.forEach(sec => sec.classList.remove('active'));
        const target = document.getElementById(sectionId);
        if (target) target.classList.add('active');
      }

      outline.addEventListener('click', function(e) {
        if (e.target.classList.contains('tree-toggle')) {
          const node = e.target.parentElement;
          node.classList.toggle('expanded');
          e.target.textContent = node.classList.contains('expanded') ? '▼' : '▶';
          return;
        }
        const item = e.target.closest('.tree-node, .tree-leaf');
        if (item && item.hasAttribute('data-section')) {
          outline.querySelectorAll('.tree-node, .tree-leaf').forEach(n => n.classList.remove('active'));
          item.classList.add('active');
          showSection(item.getAttribute('data-section'));
        }
      });

      // Show only the first section by default
      showSection('section1');
    });
  </script>
</body>
</html>