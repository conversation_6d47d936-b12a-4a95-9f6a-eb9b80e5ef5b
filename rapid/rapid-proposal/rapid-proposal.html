<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>RAPID Proposal – Product Roadmap & Architecture</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: #f7f7f7;
    }
    .container {
      display: flex;
      height: 100vh;
    }
    .sidebar {
      width: 280px;
      background: #222c36;
      color: #fff;
      padding: 0;
      box-shadow: 2px 0 8px rgba(0,0,0,0.05);
      display: flex;
      flex-direction: column;
    }
    .sidebar h2 {
      margin: 0;
      padding: 24px 24px 12px 24px;
      font-size: 1.2em;
      border-bottom: 1px solid #334;
      background: #1a2027;
    }
    .outline {
      list-style: none;
      padding: 0;
      margin: 0;
      flex: 1;
    }
    .outline li {
      padding: 16px 24px;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background 0.2s, border-color 0.2s;
    }
    .outline li.active, .outline li:hover {
      background: #2d3a4a;
      border-left: 4px solid #4fc3f7;
    }
    .tree ul {
      display: block;
    }
    .tree-node > ul {
      display: block;
    }
    .tree-node:not(.expanded) > ul {
      display: none;
    }
    .main {
      flex: 1;
      padding: 0 64px 48px 64px;
      background: #fff;
      overflow-y: auto;
      min-width: 0;
      box-sizing: border-box;
      font-size: 1.15em;
    }
    .main-header {
      width: 100%;
      background: #003366;
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 32px;
      padding: 0;
    }
    .main-header img {
      height: 100%;
      margin-left: 12px;
      object-fit: contain;
    }
    .slide {
      display: none;
      animation: fadeIn 0.4s;
    }
    .slide.active {
      display: block;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .roles-table, table {
      width: 100%;
      border-collapse: collapse;
      margin: 24px 0;
    }
    .roles-table th, .roles-table td, table th, table td {
      font-size: 0.95em;
      border: 1px solid #bbb;
      padding: 8px 12px;
      text-align: left;
    }
    .roles-table th, table th {
      background: #e3f2fd;
    }
    pre {
      background: #f4f4f4;
      padding: 12px;
      border-radius: 6px;
      overflow-x: auto;
    }
    code {
      background: #f4f4f4;
      padding: 2px 4px;
      border-radius: 4px;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }
    .tree ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <nav class="sidebar">
      <h2>Outline</h2>
      <ul class="outline tree" id="outline">
        <li class="tree-node expanded active" data-section="exec-summary">
          <span class="tree-toggle">▶</span> Executive Summary
          <ul>
            <li class="tree-leaf" data-section="exec-problem">Problem Statement</li>
            <li class="tree-leaf" data-section="exec-solution">Solution Outcome</li>
            <li class="tree-leaf" data-section="exec-benchmark">Benchmarking Progress to Date</li>
            <li class="tree-leaf" data-section="exec-agile">Solution: Agile-Waterfall to Agile-RAPID</li>
          </ul>
        </li>
        <li class="tree-node" data-section="roadmap">
          <span class="tree-toggle">▶</span> Product Roadmap
          <ul>
            <li class="tree-leaf" data-section="roadmap-portfolio">Product Portfolio</li>
            <li class="tree-leaf" data-section="roadmap-timeline">Timeline Overview</li>
            <li class="tree-leaf" data-section="roadmap-milestones">Step-by-Step Milestones</li>
            <li class="tree-leaf" data-section="roadmap-stakeholders">Stakeholder List</li>
            <li class="tree-leaf" data-section="roadmap-team">Tech Team & Resources</li>
            <li class="tree-leaf" data-section="roadmap-notes">Notes</li>
          </ul>
        </li>
        <li class="tree-node" data-section="arch">
          <span class="tree-toggle">▶</span> Architecture Design
          <ul>
            <li class="tree-leaf" data-section="arch-overview">Overview</li>
            <li class="tree-leaf" data-section="arch-components">System Components</li>
            <li class="tree-leaf" data-section="arch-flow">Interactions & Data Flow</li>
            <li class="tree-leaf" data-section="arch-diagram">Architecture Diagram</li>
            <li class="tree-leaf" data-section="arch-summary">Summary</li>
          </ul>
        </li>
        <li class="tree-node" data-section="internal-beta">
          <span class="tree-toggle">▶</span> Products
          <ul>
            <li class="tree-leaf" data-section="beta-design">Rapid Design</li>
            <li class="tree-leaf" data-section="beta-api">Rapid API Initializr</li>
            <li class="tree-leaf" data-section="rapidcode-benchmark">RapidCode</li>
          </ul>
        </li>
      </ul>
    </nav>
    <main class="main">
      <div class="main-header">
        <img src="images/westpac-logo.png" alt="Westpac Logo">
      </div>
      <!-- Executive Summary Sections -->
      <section class="slide section-content active" id="exec-summary">
        <h1>RAPID Development - Reengineering Development, Doubling Productivity</h1>
        <ul>
          <li><b>RAPID Methodology</b>
            <ul>
              <li>Agile squad model</li>
              <li>Reduced information passing</li>
              <li>Feature based end to end delivery</li>
              <li>AI code generation</li>
            </ul>
          </li>
          <li><b>RAPID AI product portfolio</b>
            <ul>
              <li>RAPID Designer</li>
              <li>RAPID API Initializr</li>
              <li>RAPID MFE Initializr</li>
              <li>RAPID Code</li>
            </ul>
          </li>
        </ul>
        <img src="images/rapid-design.png" alt="Rapid Design" style="max-width: 100%; height: auto; margin: 20px 0;">
        <img src="images/rapid-initializr.png" alt="Rapid Initializr" style="max-width: 100%; height: auto; margin: 20px 0;">
        <img src="images/rapid-code.png" alt="Rapid Code" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="exec-problem">
        <h2>Problem Statement</h2>
        <ul>
          <li>High manual effort</li>
          <li>Expensive to control the quality and standard
            <ul>
              <li>Overly complex workflow</li>
              <li>Missing unit test</li>
            </ul>
          </li>
          <li>Long wait times due to human dependencies
            <ul>
              <li>Waiting couple days is usual</li>
            </ul>
          </li>
          <li>No business value delivered in early stages
            <ul>
              <li>Up to 2 months</li>
            </ul>
          </li>
          <li>Overheads of boilerplate work</li>
        </ul>
        <h2>It's taking weeks to months to finalize the API contract</h2>
        <ul>
          <li>It's taking weeks to months to finalize the API contract (swagger / openapi)</li>
          <li>Refer to these Jira tickets:
            <ul>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-32">CTPDCF-32</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-33">CTPDCF-33</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-94">CTPDCF-94</a></li>
            </ul>
          </li>
        </ul>
      </section>
      <section class="slide section-content" id="exec-solution">
        <h2>Solution Outcome with RAPID Development</h2>
        <ul>
          <li><b>Productivity improvement</b>
            <ul>
              <li>Reduces setup time from 2 months to days</li>
              <li>Frees up developers for business logic</li>
              <li>Reduced repetitive boilerplate work</li>
              <li>Increase business value through significant improvement in BCE</li>
            </ul>
          </li>
          <li><b>Better quality</b>
            <ul>
              <li>Built-in mesh compliance and best practices by default</li>
              <li>Better unit test coverage</li>
            </ul>
          </li>
          <li><b>Cost reduction</b>
            <ul>
              <li>Supporting development process improvement to achieve 50% off the current cost by doubling the production</li>
            </ul>
          </li>
        </ul>
      </section>
      <section class="slide section-content" id="exec-benchmark">
        <h2>Benchmarking Progress to Date</h2>
        <ul>
          <li><b>API contract swagger design - Current approach vs RAPID:</b>
            <ul>
              <li>From 20+ days to 1 day</li>
            </ul>
          </li>
          <li><b>Code base scaffolding - Current approach vs RAPID:</b>
            <ul>
              <li>From 3 days to 3 hours</li>
            </ul>
          </li>
          <li><b>Full development cycle - Current approach vs RAPID:</b>
            <ul>
              <li>Expected to be doubling the productivity (to be benchmarked)</li>
            </ul>
          </li>
        </ul>
      </section>
      <section class="slide section-content" id="exec-agile">
        <h2>Regineering Agile-Waterfall to Agile-RAPID</h2>
        <ul>
          <li><a href="Contemporary-Software-Development-Model.html">Contemporary Software Development Model</a></li>
        </ul>
        <img src="images/communication-net-improvement.png" alt="Communication Net Improvement" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <!-- Internal Beta Sections -->
      <section class="slide section-content" id="internal-beta">
        <h1>Internal Beta</h1>
        <p>This section details the current state and process of the Rapid Design and Rapid API Initializr tools as experienced in the internal beta.</p>
      </section>
      <section class="slide section-content" id="beta-design">
        <h2>Rapid Design</h2>
        <h3>Problem Statement</h3>
        <ul>
          <li>It's taking weeks to months to finalize the API contract (swagger / openapi)</li>
          <li>Refer to these Jira tickets:
            <ul>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-32">CTPDCF-32</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-33">CTPDCF-33</a></li>
              <li><a href="https://jira.srv.westpac.com.au/browse/CTPDCF-94">CTPDCF-94</a></li>
            </ul>
          </li>
        </ul>
        <img src="images/swagger-design-ticket.png" alt="Swagger Design Ticket" style="max-width: 100%; height: auto; margin: 20px 0;">
        <h3>Current Defined Process</h3>
        <ul>
          <li>Solution designer define the API contract in human language</li>
          <li>Developer translate into the swagger/openapi yaml</li>
          <li>Submit to solution designer to review</li>
          <li>Solution designer endorse</li>
          <li>The finalized swagger/openapi yaml</li>
        </ul>
        <img src="images/defined-process.png" alt="Defined Process" style="max-width: 100%; height: auto; margin: 20px 0;">
        <h3>Actual Process</h3>
        <ul>
          <li>Solution designer define the API contract in human language</li>
          <li>Developer translate into the swagger/openapi yaml</li>
          <li>Submit to solution designer to review</li>
          <li>Wait couple days</li>
          <li>Solution designer review and leave comment</li>
          <li>Developer update the swagger/openapi yaml and submit to review again</li>
          <li>Wait couple days</li>
          <li>Solution designer review and leave comment</li>
          <li>Developer update the swagger/openapi yaml and submit to review again</li>
          <li>Wait couple days</li>
          <li>Solution designer review and leave comment</li>
          <li>Developer update the swagger/openapi yaml and submit to review again</li>
          <li>(Repeat above steps 5-10 times)</li>
          <li>Solution designer endorse</li>
          <li>The finalized swagger/openapi yaml</li>
        </ul>
        <p>Eventually after 1-2 months we get the finalized API contract</p>
        <img src="images/actual-proces.png" alt="Actual Process" style="max-width: 100%; height: auto; margin: 20px 0;">
        <h3>Process Reengineering using RAPID</h3>
        <ul>
          <li>Solution designer to own the swagger development</li>
          <li>Solution designer define and provide the swagger yaml</li>
          <li><b>Challenge without RAPID:</b> Writing the swagger/openapi yaml is time consuming. We have limited solution designers.</li>
        </ul>
        <img src="images/improved-process.png" alt="Improved Process" style="max-width: 100%; height: auto; margin: 20px 0;">
        <h3>Solution: RAPID Design Agent</h3>
        <ul>
          <li>Writing the swagger/openapi yaml is time consuming and we have limited solution designers.</li>
          <li>We want to best utilize the solution designer's time. That's the reason why we are adopting the current process of design-translate-endorse process.</li>
          <li>If a solution designer can write a swagger yaml easily, we could have solution designer write the swagger directly and make it part of the solution design.</li>
          <li>We have developed the AI powered design assistant RAPID (RAG Agentic Programmer Interactive Development) and it provides a swagger editor.</li>
        </ul>
        <img src="images/rapid-design.png" alt="Rapid Design" style="max-width: 100%; height: auto; margin: 20px 0;">
        <h3>Benefit</h3>
        <ul>
          <li><b>Higher Productivity:</b> Get the finalized API contract from months to days</li>
          <li><b>Improve the BCE significantly:</b> As the swagger is the prerequisite of starting the development. The earlier the swagger is finalized, the earlier development could start.</li>
          <li><b>Better quality:</b> With less unnecessary information passing, less chance of mistake will happen</li>
        </ul>
        <img src="images/bce-up.png" alt="BCE Up" style="max-width: 100%; height: auto; margin: 20px 0;">
        <h3>RAPID vs Copilot vs Traditional Way of creating the swagger</h3>
        <table class="roles-table">
          <thead>
            <tr>
              <th>Approach</th>
              <th>Time spent</th>
              <th>Pros</th>
              <th>Cons</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                Manual editing
                <ul>
                  <li>Solution designer define the API contract in human language. </li>
                  <li>Developer create the swagger yaml manually.</li>
                </ul>
              </td>
              <td>
                <ul>
                  <li>Solution designer ~2 hours to design each resource. </li>
                  <li>Developer ~6 hours for the first resource, </li>
                  <li>~2 hours for the second resource. </li>
                  <li>Review ~3 days. 10+ days waiting time for the solution designer's availability.</li>
                </ul>
              </td>
              <td></td>
              <td>Slow and painful. Back and forth review</td>
            </tr>
            <tr>
              <td>Writing Java and generate the swagger
                <ul>
                  <li>Solution designer define the API contract in human language. </li>
                  <li>Developers write Java class first with copilot.</li>
                  <li>Generate the swagger by spring. </li>
                  <li>Manually modify. </li>
                </ul>
              </td>
              <td>
                <ul>
                  <li>Solution designer ~2 hours to design each resource.</li>
                  <li>Developer ~3 hours for the first resource.</li>
                  <li>~1 hour for the second.</li>
                  <li>Review ~3 days.</li>
                  <li>10+ days waiting time for the solution designer's availability.</li>
                </ul>
              </td>
              <td>Quicker than manual. </td>
              <td>
                <ul>
                  <li>Need to prepare another workspace. </li>
                  <li>The developer still need to write class field by field. </li>
                  <li>Back and forth review</li>
                </ul>
              </td>
            </tr>
            <tr>
              <td>
                With Copilot
                <ul>
                  <li>Solution designer define the API contract in human language.</li>
                  <li>Based on a basic template and the API contract in human language.</li>
                  <li>Prepare a big prompt to AI (Copilot).</li>
                  <li>Copilot to generate. </li>
                  <li>Verify by human eyeball and fix by either another prompt or manually.</li>
                </ul>
              </td>
              <td>
                <ul>
                  <li>Solution designer ~2 hours to design each resource.</li>
                  <li>Developer ~30 mins for each resource.</li>
                  <li>Review ~3 days.</li>
                  <li>10+ days waiting time for the solution designer's availability.</li>
                </ul>
              </td>
              <td>It takes much less time for developer comparing. </td>
              <td>
                <ul>
                  <li>Every developer may have different prompt. </li>
                  <li>Solution designer review required. Back and forth review</li>
                </ul>
              </td>
            </tr>
            <tr>
              <td>
                With RAPID
                <ul>
                  <li>Solution designer use RAPID to define the API contract. </li>
                  <li>RAPID generate the swagger yaml file. </li>
                  <li>Verify by human eyeball and fix by either another prompt or manually. </li>
                </ul>
              </td>
              <td>
                40 mins for each resource.
              </td>
              <td>
                <ul>
                  <li>Have better control of the standard.</li>
                  <li>Save the current back and forth review process which normally counted by 10+ days.</li>
                  <li>Will have the final swagger file in couple hours.</li>
                </ul>
              </td>
              <td>
                We need to deploy another system (RAPID)
              </td>
            </tr>
          </tbody>
        </table>
      </section>
      <section class="slide section-content" id="beta-api">
        <h2>Rapid API Initializr</h2>
        <h3>Where are we now – scaffolding break down</h3>
        <ul>
          <li><b>Current API Scaffolding Development Workflow – High Effort, Low Value</b>
            <ul>
              <li>Run mesh codegen – Scaffold generation (~30 mins)</li>
              <li>Prepare pom.xml – Mesh standard (~500+ lines, ~4+ hours)</li>
              <li>Run maven command <code>mvn clean install</code> – (~3 mins)</li>
              <li>Write core components and unit test – Controller, Service, Mapper, Client, DB Repo (~1-2 days)</li>
              <li>Check-in & wait for code review – Human dependency (2–3 days delay)</li>
              <li>Tech Lead Review – (~0.5 day)</li>
            </ul>
          </li>
          <li>Before any business logic is written, ~3 developer-days per API spent</li>
        </ul>
        <h3>Where are we going – RAPID Build Agent</h3>
        <ul>
          <li>Automate the scaffold building</li>
          <li>Present the idea
            <ul>
              <li>Select step needed</li>
              <li>Write some prompt</li>
              <li>Write some business logic</li>
              <li>~30 minutes effort</li>
            </ul>
          </li>
          <li>Generate the code scaffold with test cases in 1 minute</li>
        </ul>
        <h3>RAPID Code vs Copilot vs Manual in scaffolding</h3>
        <table class="roles-table" style="margin-bottom: 32px;">
          <thead style="background: #b3aaff;">
            <tr>
              <th>Approach</th>
              <th>Time cost</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <b>Manual</b>
                <ul>
                  <li>Run mesh <a href="#">codegen</a> – Scaffold generation (~30 mins)</li>
                  <li>Prepare pom.xml – Mesh standard (~500+ lines, ~4+ hours)</li>
                  <li>Run <code>mvn clean install</code> – (~3 mins)</li>
                  <li>Write core components and unit test – Controller, Service, Mapper, Client, DB Repo (~1-2 days)</li>
                  <li>Check-in & wait for code review – Human dependency (2–3 days delay)</li>
                  <li>Tech <a href="#">Lead</a> Review – (~0.5 day)</li>
                </ul>
              </td>
              <td>
                3 developer-days effort +<br>
                2-3 days waiting time
              </td>
            </tr>
            <tr>
              <td>
                <b>Copilot</b>
                <ul>
                  <li>Same steps required as above.</li>
                  <li>Saving 50% on writing core components and unit test from 1-2 days to 0.5-1 day.</li>
                </ul>
              </td>
              <td>
                2 developer-days effort +<br>
                2-3 days waiting time
              </td>
            </tr>
            <tr>
              <td>
                <b>RAPID</b>
                <ul>
                  <li>Upload <a href="#">openapi</a> contract, upload <a href="#">openapi</a> contract of <a href="#">downstreams</a></li>
                  <li>Select resources to build.</li>
                  <li>Add predefined common steps</li>
                  <li>Describe idea in human language for each step if required.</li>
                  <li>Click generate and download</li>
                </ul>
              </td>
              <td>
                Within 1 hour
              </td>
            </tr>
          </tbody>
        </table>
      </section>
      <!-- RapidCode Benchmark Section -->
      <section class="slide section-content" id="rapidcode-benchmark">
        <h2>RapidCode</h2>
        <h3>Benchmark: RapidCode vs Copilot Agent</h3>
        <table class="roles-table" style="margin-bottom: 32px;">
          <thead style="background: #b3aaff;">
            <tr>
              <th>Feature</th>
              <th>RapidCode</th>
              <th>Copilot Agent</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Code Generation Speed</td>
              <td>Instant, multi-file, project-wide</td>
              <td>Fast, but mostly single-file or snippet</td>
            </tr>
            <tr>
              <td>Context Awareness</td>
              <td>Full project, architectural, and requirements context</td>
              <td>Primarily local file and prompt context</td>
            </tr>
            <tr>
              <td>Refactoring</td>
              <td>Automated, large-scale, cross-file</td>
              <td>Limited, mostly manual or prompt-driven</td>
            </tr>
            <tr>
              <td>Test Generation</td>
              <td>Integrated, generates and updates tests</td>
              <td>Available, but not always integrated</td>
            </tr>
            <tr>
              <td>IDE Integration</td>
              <td>VSCode, Web UI, API</td>
              <td>VSCode, JetBrains, others</td>
            </tr>
            <tr>
              <td>Customizability</td>
              <td>Highly customizable, supports enterprise workflows</td>
              <td>Limited customization</td>
            </tr>
            <tr>
              <td>Security & Privacy</td>
              <td>Enterprise-grade, on-prem/cloud, no code leaves org</td>
              <td>Cloud-based, code may be sent to external servers</td>
            </tr>
            <tr>
              <td>Cost</td>
              <td>Predictable, enterprise licensing</td>
              <td>Subscription per user/month</td>
            </tr>
            <tr>
              <td>Supported Languages</td>
              <td>Major languages, extensible</td>
              <td>Major languages</td>
            </tr>
            <tr>
              <td>Offline Capability</td>
              <td>Available (on-prem deployment)</td>
              <td>Not available</td>
            </tr>
          </tbody>
        </table>
        <h3>Case Study: Real-World Refactoring</h3>
        <p>
          <b>Use Case:</b> Based on the current code base and the database design (<code>/design/tables.ddl</code>), the task was to move the fields <code>occupationCode</code> and <code>sicCode</code> from <code>CFRequestFacilityDetail</code> to <code>CFRequestCustomerDetail</code> throughout the system.
        </p>
        <table class="roles-table" style="width:auto; margin-bottom: 16px;">
          <thead>
            <tr>
              <th>Tool</th>
              <th>Time Taken</th>
              <th>Completion</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Copilot Agent</td>
              <td>6 minutes</td>
              <td>~50% of the work completed</td>
            </tr>
            <tr>
              <td>RapidCode</td>
              <td>20 minutes</td>
              <td>~95% of the work completed</td>
            </tr>
          </tbody>
        </table>
        <p>
          <b>Summary:</b> Using the same prompt, Copilot Agent completed about half of the required changes in 6 minutes, leaving significant manual work. RapidCode, in contrast, completed nearly all required changes (95%) in 20 minutes, demonstrating superior project-wide context awareness and automation for complex refactoring tasks.
        </p>
        <div style="text-align: center;">
          <img src="images/rapidcode-vs-copilot-agent.png" alt="RapidCode vs Copilot Agent" style="max-width: 100%; height: auto; margin: 20px 0;">
        </div>
      </section>
      <!-- Product Roadmap Sections -->
      <section class="slide section-content" id="roadmap">
        <h1>Rapid Platform Product Roadmap (with Timeline)</h1>
        <p>This roadmap outlines the step-by-step evolution of Rapid Designer, Rapid API Initializr, Rapid UI Initializr, and the VSCode plugin from MVP to a mature, production-ready suite. Each phase includes a target delivery timeframe (by quarter), with incremental value delivered at each step.</p>
      </section>
      <section class="slide section-content" id="roadmap-portfolio">
        <h2>RAPID AI Product Portfolio</h2>
        <p>
          The RAPID AI suite consists of several products designed to accelerate and automate software development. Below is a brief description of each product:
        </p>
        <ul>
          <li><b>RAPID Designer:</b> An AI-powered design assistant for creating and editing API contracts and solution designs, streamlining the transition from requirements to implementation.</li>
          <li><b>RAPID API Initializr:</b> Automates the generation of API scaffolding and boilerplate code from OpenAPI contracts, reducing manual effort and errors.</li>
          <li><b>RAPID MFE Initializr:</b> Provides rapid scaffolding for micro-frontend (MFE) applications, enabling quick setup and integration of frontend modules.</li>
          <li><b>RAPID Knowledge Manager:</b> Centralizes and manages technical knowledge, documentation, and best practices for the development team.</li>
          <li><b>RAPID Code:</b> An advanced code generation and refactoring tool that leverages AI to boost developer productivity and code quality.</li>
        </ul>
        <h3>Product Stage Status</h3>
        <table style="width:auto; min-width:480px;">
          <thead>
            <tr>
              <th>Name</th>
              <th>PoC</th>
              <th>Alpha</th>
              <th>Internal Beta</th>
              <th>Beta</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>RAPID Designer</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">60%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">10%</td>
            </tr>
            <tr>
              <td>RAPID API Initializr</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">30%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">10%</td>
            </tr>
            <tr>
              <td>RAPID MFE Initializr</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">30%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">10%</td>
            </tr>
            <tr>
              <td>RAPID Knowledge Manager</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">50%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">60%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">10%</td>
            </tr>
            <tr>
              <td>RAPID Code</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#43a047; text-align:center;">100%</td>
              <td style="color:#fff; background:#43a047; text-align:center;">90%</td>
              <td style="color:#fff; background:#ffb300; text-align:center;">10%</td>
            </tr>
          </tbody>
        </table>
      </section>
      <section class="slide section-content" id="roadmap-timeline">
        <h2>Roadmap Timeline Overview</h2>
        <table>
          <tr>
            <th>Phase</th>
            <th>Timeframe</th>
            <th>Key Deliverables</th>
          </tr>
          <tr>
            <td><b>MVP Release</b></td>
            <td>Q3 2025</td>
            <td>Internal beta: local-only, basic UI, core features for each tool, VSCode plugin connects locally</td>
          </tr>
          <tr>
            <td><b>Stabilization & Core Completion</b></td>
            <td>Q4 2025</td>
            <td>Code refactoring, automated tests, bug fixes, critical feature gaps closed, improved reliability</td>
          </tr>
          <tr>
            <td><b>UI/UX Overhaul</b></td>
            <td>Q1 2026</td>
            <td>Modern, accessible UI, onboarding flows, consistent design, user feedback integration</td>
          </tr>
          <tr>
            <td><b>Cloud Enablement</b></td>
            <td>Q2 2026</td>
            <td>Cloud sync, user accounts, secure storage, authentication, basic cloud dashboard</td>
          </tr>
          <tr>
            <td><b>Collaboration & Integration</b></td>
            <td>Q3 2026</td>
            <td>Real-time team collaboration, unified platform, shared auth/data, enhanced VSCode plugin (cloud support)</td>
          </tr>
          <tr>
            <td><b>Extensibility & Marketplace</b></td>
            <td>Q4 2026</td>
            <td>Plugin/extension SDK, APIs, marketplace launch, extension management UI</td>
          </tr>
          <tr>
            <td><b>Enterprise Readiness & Launch</b></td>
            <td>Q1 2027</td>
            <td>Security, compliance, SSO, RBAC, audit logging, scalability, documentation, public launch</td>
          </tr>
        </table>
      </section>
      <section class="slide section-content" id="roadmap-milestones">
        <h2>Step-by-Step Milestones</h2>
        <h3>Q3 2025: MVP Release</h3>
        <ul>
          <li>Internal beta for all tools (Rapid Designer, API Initializr, UI Initializr, VSCode plugin)</li>
          <li>Local-only operation, minimal UI, core generation/scaffolding features</li>
          <li>Early adopter feedback collection</li>
        </ul>
        <h3>Q4 2025: Stabilization & Core Completion</h3>
        <ul>
          <li>Refactor codebase for maintainability and test coverage</li>
          <li>Address critical bugs and technical debt</li>
          <li>Complete core features and close major gaps</li>
          <li>Set up CI/CD and automated testing</li>
        </ul>
        <h3>Q1 2026: UI/UX Overhaul</h3>
        <ul>
          <li>Redesign UI for usability and accessibility (WCAG compliance)</li>
          <li>Implement consistent design language and onboarding flows</li>
          <li>Integrate user feedback from MVP and stabilization phases</li>
        </ul>
        <h3>Q2 2026: Cloud Enablement</h3>
        <ul>
          <li>Add cloud backend: sync, storage, authentication</li>
          <li>User account management and secure data handling</li>
          <li>Basic cloud dashboard for project management</li>
        </ul>
        <h3>Q3 2026: Collaboration & Integration</h3>
        <ul>
          <li>Real-time team collaboration (editing, commenting)</li>
          <li>Integrate tools into a unified platform with shared authentication/data</li>
          <li>Enhance VSCode plugin for cloud/remote project support</li>
        </ul>
        <h3>Q4 2026: Extensibility & Marketplace</h3>
        <ul>
          <li>Launch plugin/extension SDK and public APIs</li>
          <li>Build extension management UI</li>
          <li>Launch plugin/extension marketplace</li>
        </ul>
        <h3>Q1 2027: Enterprise Readiness & Public Launch</h3>
        <ul>
          <li>Implement SSO, RBAC, audit logging, compliance (GDPR, SOC2, etc.)</li>
          <li>Optimize for scalability and performance</li>
          <li>Prepare comprehensive documentation and onboarding resources</li>
          <li>Plan and execute public launch, establish support channels</li>
        </ul>
      </section>
      <section class="slide section-content" id="roadmap-stakeholders">
        <h2>Stakeholder List</h2>
        <ul>
          <li><b>Product Owner / Project Sponsor:</b> Sets vision, priorities, and approves major milestones.</li>
          <li><b>Product Manager:</b> Manages roadmap, requirements, and stakeholder communication.</li>
          <li><b>Engineering Team:</b>
            <ul>
              <li>Frontend Developers (Web, VSCode Extension)</li>
              <li>Backend/API Developers</li>
              <li>Cloud/DevOps Engineers</li>
              <li>QA/Test Engineers</li>
            </ul>
          </li>
          <li><b>UX/UI Designers:</b> Design user flows, wireframes, and visual assets.</li>
          <li><b>Security & Compliance:</b> Ensure platform meets security and regulatory requirements.</li>
          <li><b>Customer Success / Support:</b> Handle onboarding, feedback, and support channels.</li>
          <li><b>Beta Users / Early Adopters:</b> Provide feedback and validate features.</li>
          <li><b>Marketing & Launch Team:</b> Prepare go-to-market, documentation, and launch activities.</li>
        </ul>
      </section>
      <section class="slide section-content" id="roadmap-team">
        <h2>Tech Team & Resource Requirements</h2>
        <table>
          <tr>
            <th>Role</th>
            <th>Q3 2025 (MVP)</th>
            <th>Q4 2025 (Stabilize)</th>
            <th>Q1 2026 (UI/UX)</th>
            <th>Q2 2026 (Cloud)</th>
            <th>Q3 2026 (Collab)</th>
            <th>Q4 2026 (Extensibility)</th>
            <th>Q1 2027 (Enterprise)</th>
          </tr>
          <tr>
            <td>Product Manager</td>
            <td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td>
          </tr>
          <tr>
            <td>Frontend Developers</td>
            <td>2</td><td>2</td><td>3</td><td>3</td><td>3</td><td>3</td><td>2</td>
          </tr>
          <tr>
            <td>Backend/API Developers</td>
            <td>1</td><td>2</td><td>2</td><td>3</td><td>3</td><td>3</td><td>2</td>
          </tr>
          <tr>
            <td>Cloud/DevOps Engineers</td>
            <td>0</td><td>1</td><td>1</td><td>2</td><td>2</td><td>2</td><td>2</td>
          </tr>
          <tr>
            <td>QA/Test Engineers</td>
            <td>1</td><td>2</td><td>2</td><td>2</td><td>2</td><td>2</td><td>2</td>
          </tr>
          <tr>
            <td>UX/UI Designers</td>
            <td>1</td><td>1</td><td>2</td><td>1</td><td>1</td><td>1</td><td>1</td>
          </tr>
          <tr>
            <td>Security/Compliance</td>
            <td>0</td><td>0</td><td>0</td><td>1</td><td>1</td><td>1</td><td>2</td>
          </tr>
          <tr>
            <td>Customer Success/Support</td>
            <td>0</td><td>0</td><td>0</td><td>1</td><td>1</td><td>2</td><td>2</td>
          </tr>
        </table>
        <p><i>Numbers are indicative FTEs (full-time equivalents) and may be adjusted based on project needs and velocity.</i></p>
      </section>
      <section class="slide section-content" id="roadmap-notes">
        <h2>Notes</h2>
        <ul>
          <li>Timeline is indicative and should be reviewed quarterly.</li>
          <li>Early feedback and iterative releases are encouraged at each phase.</li>
          <li>Adjustments may be made based on user feedback, technical challenges, or business priorities.</li>
        </ul>
        <p><b>This roadmap is a living document and should be updated as the project evolves.</b></p>
      </section>
      <!-- Architecture Design Sections -->
      <section class="slide section-content" id="arch">
        <h1>RAPID Architecture Design</h1>
        <p>RAPID (RAG Agentic Programmer Interactive Development) is a cloud-native, Azure-based code generation ecosystem. It provides a Web UI portal, a VS Code plugin, and deep integration with Azure AI services and external platforms for secure, scalable, and interactive development.</p>
      </section>
      <section class="slide section-content" id="arch-overview">
        <h2>Overview</h2>
        <p>RAPID (RAG Agentic Programmer Interactive Development) is a cloud-native, Azure-based code generation ecosystem. It provides a Web UI portal, a VS Code plugin, and deep integration with Azure AI services and external platforms for secure, scalable, and interactive development.</p>
        <img src="images/architecture-diagram.png" alt="Architecture Diagram" style="max-width: 100%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="arch-components">
        <h2>Major System Components & Responsibilities</h2>
        <ol>
          <li><b>Web UI Portal (React)</b>
            <ul>
              <li>User-facing portal with modules for:
                <ul>
                  <li>Rapid Designer</li>
                  <li>Rapid API Initializr</li>
                  <li>Rapid UI Initializr</li>
                  <li>Knowledge Manager</li>
                </ul>
              </li>
              <li>Authenticates via Azure AD</li>
              <li>Communicates with the backend via secure REST APIs</li>
            </ul>
          </li>
          <li><b>Backend API (Node.js/Express)</b>
            <ul>
              <li>Orchestrates business logic, user/session management, and all integrations</li>
              <li>Connects to Azure AI services (OpenAI, AI Search, Prompt Flow, Logic Apps, Data Factory, Functions)</li>
              <li>Integrates with external platforms (SharePoint, Confluence, Bitbucket)</li>
              <li>Handles secure storage and secrets (Azure Blob Storage, Key Vault)</li>
            </ul>
          </li>
          <li><b>VS Code Plugin (TypeScript)</b>
            <ul>
              <li>In-editor code generation, suggestions, and knowledge search</li>
              <li>Authenticates via Azure AD</li>
              <li>Communicates with backend API and, optionally, Azure OpenAI</li>
            </ul>
          </li>
          <li><b>Azure AI Services</b>
            <ul>
              <li>Azure OpenAI (GPT-4, ada-text-002): Code generation, chat, summarization</li>
              <li>Azure AI Search: Semantic/document/code search</li>
              <li>Azure Prompt Flow: Orchestrate multi-step AI workflows</li>
              <li>Azure Logic Apps/Data Factory: Data integration and automation</li>
              <li>Azure Functions: Serverless compute for custom logic</li>
              <li>Azure Blob Storage: Store code, assets, and knowledge</li>
              <li>Azure Key Vault: Secure secrets and credentials</li>
              <li>Azure AD: Centralized authentication and authorization</li>
            </ul>
          </li>
          <li><b>External Integrations</b>
            <ul>
              <li>SharePoint: Knowledge/document retrieval</li>
              <li>Confluence: Documentation and knowledge base integration</li>
              <li>Bitbucket: Source code repository integration</li>
            </ul>
          </li>
          <li><b>Security and Compliance</b>
            <ul>
              <li>All user authentication and authorization via Azure AD</li>
              <li>Data encrypted at rest (Blob Storage, Key Vault) and in transit (HTTPS)</li>
              <li>Follows Azure security best practices</li>
            </ul>
          </li>
        </ol>
      </section>
      <section class="slide section-content" id="arch-flow">
        <h2>Interactions & Data Flow</h2>
        <ul>
          <li>All users authenticate via Azure AD.</li>
          <li>Web UI and VS Code plugin send REST API requests to the backend, including Azure AD tokens.</li>
          <li>Backend API validates tokens, orchestrates AI and search requests, and manages external integrations.</li>
          <li>Backend and (optionally) VS Code plugin interact with Azure AI Services for RAG, code generation, and search.</li>
          <li>All secrets and assets are managed via Azure Blob Storage and Key Vault.</li>
          <li>All communication is encrypted and follows Azure security best practices.</li>
        </ul>
        <h3>Main Data Flows:</h3>
        <ul>
          <li>User → (Azure AD) → Web UI/VS Code → (REST) → Backend API → (Azure AI Services, External Integrations, Storage)</li>
          <li>Backend API ↔ Azure AI Services (OpenAI, Search, etc.)</li>
          <li>Backend API ↔ SharePoint/Confluence/Bitbucket</li>
          <li>Backend API ↔ Azure Blob Storage/Key Vault</li>
        </ul>
      </section>
      <section class="slide section-content" id="arch-diagram">
        <h2>High-Level Architecture Diagram</h2>
        <pre><code>
```mermaid
flowchart TD
  subgraph User Devices
    A1[User]
    A2[VS Code with Rapid-code Plugin]
    A1 -- Uses --> UI[Web UI Portal]
    A2 -- Auth via Azure AD --> AD[Azure AD]
    UI -- Auth via Azure AD --> AD
  end

  UI -- REST API Calls --> API[Backend API]
  A2 -- REST API Calls --> API

  subgraph Azure AI Services
    OAI[Azure OpenAI]
    AIS[Azure AI Search]
    PF[Azure Prompt Flow]
    LA[Azure Logic Apps]
    DF[Azure Data Factory]
    AF[Azure Functions]
    BS[Azure Blob Storage]
    KV[Azure Key Vault]
    AD
  end

  API -- Auth/Token Validation --> AD
  API -- RAG, Code Gen, Search --> OAI
  API -- Semantic Search --> AIS
  API -- Orchestration --> PF
  API -- Automation --> LA
  API -- Data Integration --> DF
  API -- Serverless Logic --> AF
  API -- Store/Retrieve Assets --> BS
  API -- Secrets Management --> KV

  subgraph External Integrations
    SP[SharePoint]
    CF[Confluence]
    BB[Bitbucket]
  end

  API -- Knowledge/Code Retrieval --> SP
  API -- Documentation Integration --> CF
  API -- Source Code Integration --> BB

  A2 -- Direct RAG/AI Calls (optional) --> OAI

  classDef azure fill:#e6f7ff,stroke:#0078d4,stroke-width:2px;
  class OAI,AIS,PF,LA,DF,AF,BS,KV,AD azure;
```
        </code></pre>
        <p><i>(For a rendered diagram, use a Mermaid-enabled viewer.)</i></p>
      </section>
      <section class="slide section-content" id="arch-summary">
        <h2>Concise Architecture Summary</h2>
        <p>
          RAPID is a secure, cloud-native code generation platform leveraging Azure for authentication, AI, search, storage, and automation. The React Web UI and VS Code plugin provide user interfaces, both authenticating via Azure AD and communicating with a Node.js/Express backend. The backend orchestrates all business logic, AI workflows, and integrations with SharePoint, Confluence, and Bitbucket, while managing assets and secrets in Azure Blob Storage and Key Vault. All communication is encrypted and compliant with Azure security standards.
        </p>
      </section>
    </main>
  </div>
  <script>
    // Tree menu navigation logic with event delegation
    document.addEventListener('DOMContentLoaded', function() {
      const outline = document.getElementById('outline');
      const sections = document.querySelectorAll('.section-content');

      // Make all tree-nodes foldable, like the reference
      outline.querySelectorAll('.tree-node').forEach(function(node) {
        if (node.querySelector('ul')) {
          // Only expand the first node by default
          if (node === outline.querySelector('.tree-node')) {
            node.classList.add('expanded');
            node.querySelector('.tree-toggle').textContent = '▼';
          } else {
            node.classList.remove('expanded');
            node.querySelector('.tree-toggle').textContent = '▶';
          }
        }
      });

      function showSection(sectionId) {
        sections.forEach(sec => sec.classList.remove('active'));
        const target = document.getElementById(sectionId);
        if (target) target.classList.add('active');
      }

      outline.addEventListener('click', function(e) {
        if (e.target.classList.contains('tree-toggle')) {
          const node = e.target.parentElement;
          node.classList.toggle('expanded');
          e.target.textContent = node.classList.contains('expanded') ? '▼' : '▶';
          return;
        }
        const item = e.target.closest('.tree-node, .tree-leaf');
        if (item && item.hasAttribute('data-section')) {
          outline.querySelectorAll('.tree-node, .tree-leaf').forEach(n => n.classList.remove('active'));
          item.classList.add('active');
          showSection(item.getAttribute('data-section'));
        }
      });

      // Show only the first section by default
      showSection('exec-summary');
    });
  </script>
</body>
</html>