# RAPID Architecture Design

## Overview

RAPID (RAG Agentic Programmer Interactive Development) is a cloud-native, Azure-based code generation ecosystem. It provides a Web UI portal, a VS Code plugin, and deep integration with Azure AI services and external platforms for secure, scalable, and interactive development.

---

## Major System Components & Responsibilities

### 1. Web UI Portal (React)
- User-facing portal with modules for:
  - Rapid Designer
  - Rapid API Initializr
  - Rapid UI Initializr
  - Knowledge Manager
- Authenticates via Azure AD
- Communicates with the backend via secure REST APIs

### 2. Backend API (Node.js/Express)
- Orchestrates business logic, user/session management, and all integrations
- Connects to Azure AI services (OpenAI, AI Search, Prompt Flow, Logic Apps, Data Factory, Functions)
- Integrates with external platforms (SharePoint, Confluence, Bitbucket)
- Handles secure storage and secrets (Azure Blob Storage, Key Vault)

### 3. VS Code Plugin (TypeScript)
- In-editor code generation, suggestions, and knowledge search
- Authenticates via Azure AD
- Communicates with backend API and, optionally, Azure OpenAI

### 4. Azure AI Services
- Azure OpenAI (GPT-4, ada-text-002): Code generation, chat, summarization
- Azure AI Search: Semantic/document/code search
- Azure Prompt Flow: Orchestrate multi-step AI workflows
- Azure Logic Apps/Data Factory: Data integration and automation
- Azure Functions: Serverless compute for custom logic
- Azure Blob Storage: Store code, assets, and knowledge
- Azure Key Vault: Secure secrets and credentials
- Azure AD: Centralized authentication and authorization

### 5. External Integrations
- SharePoint: Knowledge/document retrieval
- Confluence: Documentation and knowledge base integration
- Bitbucket: Source code repository integration

### 6. Security and Compliance
- All user authentication and authorization via Azure AD
- Data encrypted at rest (Blob Storage, Key Vault) and in transit (HTTPS)
- Follows Azure security best practices

---

## Interactions & Data Flow

- All users authenticate via Azure AD.
- Web UI and VS Code plugin send REST API requests to the backend, including Azure AD tokens.
- Backend API validates tokens, orchestrates AI and search requests, and manages external integrations.
- Backend and (optionally) VS Code plugin interact with Azure AI Services for RAG, code generation, and search.
- All secrets and assets are managed via Azure Blob Storage and Key Vault.
- All communication is encrypted and follows Azure security best practices.

**Main Data Flows:**
- User → (Azure AD) → Web UI/VS Code → (REST) → Backend API → (Azure AI Services, External Integrations, Storage)
- Backend API ↔ Azure AI Services (OpenAI, Search, etc.)
- Backend API ↔ SharePoint/Confluence/Bitbucket
- Backend API ↔ Azure Blob Storage/Key Vault

---

## High-Level Architecture Diagram

```mermaid
flowchart TD
  subgraph User Devices
    A1[User]
    A2[VS Code with Rapid-code Plugin]
    A1 -- Uses --> UI[Web UI Portal]
    A2 -- Auth via Azure AD --> AD[Azure AD]
    UI -- Auth via Azure AD --> AD
  end

  UI -- REST API Calls --> API[Backend API]
  A2 -- REST API Calls --> API

  subgraph Azure AI Services
    OAI[Azure OpenAI]
    AIS[Azure AI Search]
    PF[Azure Prompt Flow]
    LA[Azure Logic Apps]
    DF[Azure Data Factory]
    AF[Azure Functions]
    BS[Azure Blob Storage]
    KV[Azure Key Vault]
    AD
  end

  API -- Auth/Token Validation --> AD
  API -- RAG, Code Gen, Search --> OAI
  API -- Semantic Search --> AIS
  API -- Orchestration --> PF
  API -- Automation --> LA
  API -- Data Integration --> DF
  API -- Serverless Logic --> AF
  API -- Store/Retrieve Assets --> BS
  API -- Secrets Management --> KV

  subgraph External Integrations
    SP[SharePoint]
    CF[Confluence]
    BB[Bitbucket]
  end

  API -- Knowledge/Code Retrieval --> SP
  API -- Documentation Integration --> CF
  API -- Source Code Integration --> BB

  A2 -- Direct RAG/AI Calls (optional) --> OAI

  classDef azure fill:#e6f7ff,stroke:#0078d4,stroke-width:2px;
  class OAI,AIS,PF,LA,DF,AF,BS,KV,AD azure;
```

---

## Concise Architecture Summary

RAPID is a secure, cloud-native code generation platform leveraging Azure for authentication, AI, search, storage, and automation. The React Web UI and VS Code plugin provide user interfaces, both authenticating via Azure AD and communicating with a Node.js/Express backend. The backend orchestrates all business logic, AI workflows, and integrations with SharePoint, Confluence, and Bitbucket, while managing assets and secrets in Azure Blob Storage and Key Vault. All communication is encrypted and compliant with Azure security standards.

---