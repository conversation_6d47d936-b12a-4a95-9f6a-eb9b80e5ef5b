# RAPID Development - Reengineering Development, Doubling Productivity

---

## Problem Statement

- High manual effort
- Expensive to control the quality and standard
  - Overly complex workflow
  - Missing unit test
- Long wait times due to human dependencies
  - Waiting couple days is usual
- No business value delivered in early stages
  - Up to 2 months
- Overheads of boilerplate work

---

## Solution Outcome with RAPID Development

- **Productivity improvement**
  - Reduces setup time from 2 months to days
  - Frees up developers for business logic
  - Reduced repetitive boilerplate work
  - Increase business value through significant improvement in BCE
- **Better quality**
  - Built-in mesh compliance and best practices by default
  - Better unit test coverage
- **Cost reduction**
  - Supporting development process improvement to achieve 50% off the current cost by doubling the production

---

## Benchmarking Progress to Date

- **API contract swagger design - Current approach vs RAPID**
  - From 20+ days to 1 day
- **Code base scaffolding - Current approach vs RAPID**
  - From 3 days to 3 hours
- **Full development cycle - Current approach vs RAPID**
  - Expected to be doubling the productivity
  - To be benchmarked

---

## It's taking weeks to months to finalize the API contract

- It's taking weeks to months to finalize the API contract (swagger / openapi)
- Refer to these Jira tickets:
  - https://jira.srv.westpac.com.au/browse/CTPDCF-32
  - https://jira.srv.westpac.com.au/browse/CTPDCF-33
  - https://jira.srv.westpac.com.au/browse/CTPDCF-94

---

## Low Level Design – Swagger Spec

### Current Defined Process

- Define – Translate – Review - Endorse
  - Solution designer define the API contract in human language
  - Developer translate into the swagger/openapi yaml
  - Submit to solution designer to review
  - Solution designer endorse
  - The finalized swagger/openapi yaml

### Actual Process

- Define – Translate – Review - Translate – Review ..... Endorse
  - Solution designer define the API contract in human language
  - Developer translate into the swagger/openapi yaml
  - Submit to solution designer to review
  - Wait couple days
  - Solution designer review and leave comment
  - Developer update the swagger/openapi yaml and submit to review again
  - (Repeat above steps 5-10 times)
  - Solution designer endorse
  - The finalized swagger/openapi yaml

- Eventually after 1-2 months we get the finalized API contract

---

## Process Reengineering using RAPID

- Solution designer to own the swagger development
- Solution designer define and provide the swagger yaml
- **Challenge without RAPID:**
  - Writing the swagger/openapi yaml is time consuming. We have limited solution designers.

---

## Solution: RAPID Design Agent

- As per the challenge, writing the swagger/openapi yaml is time consuming and we have limited solution designers.
- We want to best utilize the solution designer's time. That's the reason why we are adopting the current process of design-translate-endorse process.
- If a solution designer can write a swagger yaml easily, we could have solution designer write the swagger directly and make it part of the solution design.
- We have developed the AI powered design assistant RAPID (RAG Agentic Programmer Interactive Development) and it provides a swagger editor.

---

## Benefit

- **Higher Productivity**
  - Get the finalized API contract from months to days
- **Improve the BCE significantly**
  - As the swagger is the prerequisite of starting the development. The earlier the swagger is finalized, the earlier development could start.
- **Better quality**
  - With less unnecessary information passing, less chance of mistake will happen

---

## RAPID vs Copilot vs Traditional Way of creating the swagger

### Appendix - Benchmark

| Approach | Time spent | Pros | Cons |
|----------|------------|------|------|
| Manual editing | Solution designer define the API contract in human language. Developer create the swagger yaml manually. Solution designer ~2 hours to design each resource. Developer ~6 hours for the first resource, ~2 hours for the second resource. Review ~3 days. 10+ days waiting time for the solution designer's availability. | Slow and painful. Back and forth review |  |
| Writing Java and generate the swagger | Solution designer define the API contract in human language. Write Java class first with copilot. Generate the swagger by spring. Manually modify. Solution designer ~2 hours to design each resource. Developer ~3 hours for the first resource, ~1 hour for the second. Review ~3 days. 10+ days waiting time for the solution designer's availability. | Quicker than manual. Need to prepare another workspace. The developer still need to write class field by field. Back and forth review |  |
| With Copilot | Solution designer define the API contract in human language. Based on a basic template and the API contract in human language. Prepare a big prompt to AI (Copilot). Copilot to generate. Verify by human eyeball and fix by either another prompt or manually. Solution designer ~2 hours to design each resource. Developer ~30 mins for each resource. Review ~3 days. 10+ days waiting time for the solution designer's availability. | It takes much less time for developer comparing manual and Java. Every developer may have different prompt. Solution designer review required. Back and forth review |  |
| With RAPID | Solution designer use RAPID to define the API contract. RAPID generate the swagger yaml file. Verify by human eyeball and fix by either another prompt or manually. 40 mins for each resource. | Have better control of the standard. Save the current back and forth review process which normally counted by 10+ days. Will have the final swagger file in couple hours. We need to deploy another system (RAPID) |  |

---

## Where are we now – scaffolding break down

- **Current API Scaffolding Development Workflow – High Effort, Low Value**
  - Run mesh codegen – Scaffold generation (~30 mins)
  - Prepare pom.xml – Mesh standard (~500+ lines, ~4+ hours)
  - Run maven command `mvn clean install` – (~3 mins)
  - Write core components and unit test – Controller, Service, Mapper, Client, DB Repo (~1-2 days)
  - Check-in & wait for code review – Human dependency (2–3 days delay)
  - Tech Lead Review – (~0.5 day)
- Before any business logic is written, ~3 developer-days per API spent

---

## Where are we going – RAPID Build Agent

- Automate the scaffold building
- Present the idea
  - Select step needed
  - Write some prompt
  - Write some business logic
  - ~30 minutes effort
- Generate the code scaffold with test cases in 1 minute

---

## Agile-Waterfall to Agile-RAPID

- [Contemporary Software Development Model (Medium)](https://medium.com/@wangador/contemporary-software-development-model-6c675849a9f8)

| Vision | Doubling Productivity with Reengineering |
|--------|-----------------------------------------|
| Current | Agile-Waterfall |
| Improved | Agile-RAPID |

---

## RAPID Code vs Copilot vs Manual in scaffolding

| Approach | Time cost |
|----------|-----------|
| Manual | Run mesh codegen – Scaffold generation (~30 mins). Prepare pom.xml – Mesh standard (~500+ lines, ~4+ hours). Run 'mvn clean install' – (~3 mins). Write core components and unit test – Controller, Service, Mapper, Client, DB Repo (~1-2 days). Check-in & wait for code review – Human dependency (2–3 days delay). Tech Lead Review – (~0.5 day). 3 developer-days effort + 2-3 days waiting time. |
| Copilot | Same steps required as above. Saving 50% on writing core components and unit test from 1-2 days to 0.5-1 day. 2 developer-days effort + 2-3 days waiting time. |
| RAPID | Upload openapi contract, upload openapi contract of downstreams. Select resources to build. Add predefined common steps. Describe idea in human language for each step if required. Click generate and download. Within 1 hour. |

---

## RAPID vs Copilot in Business Logic Development

- To be benchmarked

| Approach | Pros and Cons |
|----------|--------------|
| Copilot in IDE | Can read the codes in same repository. Manual copy business logic from jira/confluence and paste to IDE copilot and generate. Cannot paste too much, the copilot will hang. Do small chunks step by step. Copy-paste the generated code to IDE. Save some manual work on typing the codes and test. Less mistakes. Need to copy-paste the business to IDE. Generate code snippet only. Manually copy to files. |
| Copilot in Edge | Can read Jira/Confluence page. Not knowing the existing codes. Code generated is not Westpac standard compliant. Save some manual work on copy-paste the business logic and typing the codes and test. Need to copy-paste existing code to Edge. Generate code snippet only. Manually copy to files. |
| RAPID integrated with Jira and Confluence | Upload openapi contract, upload openapi contract of downstreams. Select resources to build. Add predefined common steps. Describe idea in human language for each step if required. Click generate and download. Save more time than above. Having both benefit of above. Generate multiple file in one go. |

---

## Thank you
