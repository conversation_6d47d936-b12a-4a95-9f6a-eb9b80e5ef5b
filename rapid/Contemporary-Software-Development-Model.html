<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Contemporary Software Development Model - Presentation</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: #f7f7f7;
    }
    .container {
      display: flex;
      height: 100vh;
    }
    .sidebar {
      width: 280px;
      background: #222c36;
      color: #fff;
      padding: 0;
      box-shadow: 2px 0 8px rgba(0,0,0,0.05);
      display: flex;
      flex-direction: column;
    }
    .sidebar h2 {
      margin: 0;
      padding: 24px 24px 12px 24px;
      font-size: 1.2em;
      border-bottom: 1px solid #334;
      background: #1a2027;
    }
    .outline {
      list-style: none;
      padding: 0;
      margin: 0;
      flex: 1;
    }
    .outline li {
      padding: 16px 24px;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background 0.2s, border-color 0.2s;
    }
    .outline li.active, .outline li:hover {
      background: #2d3a4a;
      border-left: 4px solid #4fc3f7;
    }
    .main {
      flex: 1;
      padding: 0 64px 48px 64px;
      background: #fff;
      overflow-y: auto;
      min-width: 0;
      box-sizing: border-box;
      font-size: 1.25em;
    }
    .main-header {
      width: 100%;
      background: #003366;
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 32px;
      padding: 0;
    }
    .main-header img {
      height: 100%;
      margin-left: 12px;
      object-fit: contain;
    }
    .slide {
      display: none;
      animation: fadeIn 0.4s;
    }
    .slide.active {
      display: block;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .roles-table {
      width: 100%;
      border-collapse: collapse;
      margin: 24px 0;
    }
    .roles-table th, .roles-table td {
      font-size: 0.75em;
      border: 1px solid #bbb;
      padding: 8px 12px;
      text-align: left;
    }
    .roles-table th {
      background: #e3f2fd;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px);}
      to { opacity: 1; transform: translateY(0);}
    }
  </style>
    <style>
      .tree {
        padding-left: 0;
        list-style: none;
      }
      .tree-node, .tree-leaf {
        padding: 10px 24px 10px 36px;
        cursor: pointer;
        border-left: 4px solid transparent;
        transition: background 0.2s, border-color 0.2s;
        user-select: none;
      }
      .tree-node {
        font-weight: bold;
        position: relative;
        background: #222c36;
      }
      .tree-leaf {
        font-weight: normal;
        background: #263142;
        padding-left: 56px;
      }
      .tree-node.active, .tree-leaf.active {
        background: #2d3a4a;
        border-left: 4px solid #4fc3f7;
      }
      .tree-toggle {
        display: inline-block;
        width: 18px;
        margin-left: -18px;
        margin-right: 6px;
        font-size: 0.9em;
        color: #4fc3f7;
        vertical-align: middle;
      }
      .tree-node:not(.expanded) > .tree-toggle {
        transform: rotate(-90deg);
      }
      .tree-node > ul {
        display: block;
        margin: 0;
        padding: 0;
      }
      .tree-node:not(.expanded) > ul {
        display: none;
      }
    </style>
    <style>
      /* Remove bullets/circles from all nested menu levels */
      .tree ul {
        list-style: none;
        margin: 0;
        padding: 0;
      }
    </style>
</head>
<body>
  <div class="container">
    <nav class="sidebar">
      <h2>Outline</h2>
      <ul class="outline tree" id="outline">
        <li class="tree-node expanded active" data-section="section1">
          <span class="tree-toggle">▼</span> Contemporary Software Development Model
        </li>
        <li class="tree-node expanded" data-section="section2">
          <span class="tree-toggle">▼</span> Waterfall Model (1980s)
          <ul>
            <li class="tree-leaf" data-section="section2-1">Benefits of Waterfall</li>
            <li class="tree-leaf" data-section="section2-2">Context and Limitations</li>
            <li class="tree-leaf" data-section="section2-3">Roles, Responsibilities, and Challenges in Waterfall</li>
            <li class="tree-leaf" data-section="section2-4">Communication Overheads</li>
          </ul>
        </li>
        <li class="tree-node expanded" data-section="section3">
          <span class="tree-toggle">▼</span> Agile Squad Model (2010s)
          <ul>
            <li class="tree-leaf" data-section="section3-1">Key Changes</li>
            <li class="tree-leaf" data-section="section3-2">Benefits of the Agile Model</li>
            <li class="tree-leaf" data-section="section3-3">Roles, Responsibilities, and Challenges in Agile</li>
            <li class="tree-leaf" data-section="section3-4">Communication Net</li>
          </ul>
        </li>
        <li class="tree-node" data-section="section4">
          <span class="tree-toggle">▶</span> Conclusion
        </li>
      </ul>
    </nav>
    <main class="main">
      <div class="main-header">
        <img src="images/westpac-logo.png" alt="Westpac Logo">
      </div>
      <section class="slide section-content active" id="section1">
        <h1>Contemporary Software Development Model</h1>
        <p>
          The evolution of software development models reflects the industry’s ongoing quest for more efficient, flexible, and reliable methods of creating software. From the early days of ad-hoc programming to today’s agile and DevOps approaches, each new model has sought to address the shortcomings of its predecessors while adapting to changing technological landscapes and business needs.
        </p>
      </section>
      <section class="slide section-content" id="section2">
        <h2>Waterfall Model (1980s)</h2>
        <p>
          The Waterfall model, introduced by Winston W. Royce in 1970, was one of the first formalized approaches to software development. This linear sequential model divided the development process into distinct phases:
        </p>
        <ul>
          <li>Requirements</li>
          <li>Design</li>
          <li>Implementation</li>
          <li>Verification</li>
          <li>Maintenance</li>
        </ul>
      </section>
      <section class="slide section-content" id="section2-1">
        <h3>Benefits of Waterfall</h3>
        <ol>
          <li>Clear structure: Linear sequence of phases, easy to understand and manage.</li>
          <li>Defined deliverables: Each phase has specific deliverables and a review process.</li>
          <li>Predictability: Easier to estimate costs, timelines, and resources.</li>
          <li>Documentation: Emphasizes comprehensive documentation at each stage.</li>
          <li>Suitable for stable projects: Works well for projects with well-understood, unchanging requirements.</li>
          <li>Clear milestones: Distinct milestones throughout the process.</li>
          <li>Less client involvement required after requirements phase.</li>
          <li>Easier to manage for less experienced teams.</li>
          <li>Enforces discipline: Each phase must be completed before moving to the next.</li>
        </ol>
      </section>
      <section class="slide section-content" id="section2-2">
        <h3>Context and Limitations</h3>
        <div>
          <p>
            <b>When the Waterfall was popular:</b><br>
            There was no frameworks, no cloud, no CI/CD, no containers, no microservices. Development was hard, developers needed to write a lot of code, and there was no test automation.
          </p>
          <p>
            <b>But after 50 years, the technology world has changed.</b><br>
            With the latest technology evolutions, most of the benefits of Waterfall are no longer required. So the downsides of it become more apparent.<br>
            While straightforward, the Waterfall model’s rigidity and lack of flexibility in accommodating changes led to the exploration of more iterative approaches.
          </p>
        </div>
        <p>
          Let’s break it down and see more details on how Waterfall works and the challenges when we work in the Waterfall model.
        </p>
      </section>
      <section class="slide section-content" id="section2-3">
        <h3>Roles, Responsibilities, and Challenges in Waterfall</h3>
        <p>
          The table below provides a comprehensive overview of the key roles in our current agile model. For each role, we've detailed their primary responsibilities, required knowledge, and the challenges they typically face. To provide a quick visual reference, we've used a color-coding system to indicate the difficulty level of each aspect:
        </p>
        <ul>
          <li>🟢 <b>Green:</b> Relatively easy or straightforward</li>
          <li>🟠 <b>Orange:</b> Moderate difficulty or complexity</li>
          <li>🔴 <b>Red:</b> Challenging or requiring advanced skills</li>
        </ul>
        <p>
          From this table we see couple roles are facing some challenges, some roles are facing same challenges, and the communication between roles are also challenging.
        </p>
        <p>
          We can see a few overheads from the table as well.
        </p>
        <p>
          The key of success relies on multiple factor. Each steps are critically contributing to the success of the project.
        </p>
        <div style="overflow-x:auto;">
        <table class="roles-table">
          <thead>
            <tr>
              <th>Role</th>
              <th>Responsibility</th>
              <th>Knowledge required</th>
              <th>Challenge</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Business/user</td>
              <td>Define the business requirement<br>Final decision of the product and delivery time</td>
              <td>
                <span style="color: #388e3c;">Understand the business and the value</span>
              </td>
              <td>
                Impossible triangle: scope-time-quality
              </td>
            </tr>
            <tr>
              <td>Tech BA</td>
              <td>
                Write functional spec (epic, user stories)<br>
                &bull; Analysis the requirement<br>
                &bull; Feature break down<br>
                Communicate the functional spec to PM, Solution designer, SE, QA
              </td>
              <td>
                <span style="color: #388e3c;">Understand the requirement on <span style="color: #d32f2f;">detail level <span style="color: #d32f2f;">down to data details</span></span></span><br>
                <span style="color: #388e3c;"><span style="color: #f57c00;">Understand the inbound/outbound data</span></span><br>
                <span style="color: #388e3c;">Understand the technologies picture in the organization</span><br>
                <span style="color: #388e3c;"><span style="color: #f57c00;">Understand the development methodology</span></span><br>
                <span style="color: #d32f2f;">Understand the information needed by each audience</span>
              </td>
              <td>
                It's hard to fully understand the details down to data level<br>
                PM, SD, SE, QA: they all have different angle of focus on the functional spec. Hard to communicate well to each of them.
              </td>
            </tr>
            <tr>
              <td>Project manager/project coordinator/Scrum master</td>
              <td>
                Monitor the project progress and status<br>
                Manage epics and tickets<br>
                Report to stakeholders<br>
                Coordinate any communication required<br>
                Release planning
              </td>
              <td>
                <span style="color: #388e3c;">Understand the technologies picture in the organization</span><br>
                <span style="color: #f57c00;">Understand the feature break down (from BA)</span><br>
                <span style="color: #f57c00;">Understand the technical break down (from SD)</span>
              </td>
              <td>
                PM, SD, SE, QA: they all have different angle of focus on the functional spec. Hard to communicate well to each of them.
              </td>
            </tr>
            <tr>
              <td>Solution designer</td>
              <td>
                Design the technical solution<br>
                &bull; Technical components break down<br>
                &bull; Define technical spec (HLD)<br>
                &bull; Define non-functional spec<br>
                Communicate the technical solution to SE and QA and PM
              </td>
              <td>
                <span style="color: #388e3c;">Understand the business and the value</span><br>
                <span style="color: #388e3c;">Understand the technologies picture in the organization</span><br>
                <span style="color: #388e3c;">Understand the technology governance</span><br>
                <span style="color: #388e3c;">Understand the requirement on <span style="color: #d32f2f;">detail level <span style="color: #d32f2f;">down to data details</span></span></span><br>
                <span style="color: #388e3c;">Understand the inbound/outbound <span style="color: #d32f2f;">data details</span><br>
                <span style="color: #388e3c;">Understand the feature break down from functional spec</span><br>
                <span style="color: #388e3c;"><span style="color: #f57c00;">Understand the development technologies</span></span><br>
                <span style="color: #d32f2f;">Understand the information needed by each audience</span>
              </td>
              <td>
                It's hard to fully understand the details down to data level<br>
                Need to understand the development technologies to make the best design so that the development is easy and less chance to create bugs<br>
                PM, SE, QA: they all have different angle of focus on the functional spec. Hard to communicate well to each of them.
              </td>
            </tr>
            <tr>
              <td>Software engineer</td>
              <td>
                Application design (LLD)<br>
                &bull; API design (swagger)<br>
                &bull; microservice design<br>
                Development<br>
                &bull; Feature codes<br>
                &bull; Unit test
              </td>
              <td>
                <span style="color: #388e3c;">Understand the functional spec <span style="color: #d32f2f;">down to data details</span></span><br>
                <span style="color: #388e3c;">Understand the technical solution <span style="color: #d32f2f;">down to data details</span></span><br>
                <span style="color: #388e3c;">Understand the latest best practice and principal</span><br>
                <span style="color: #388e3c;">Understand the development technologies</span>
              </td>
              <td>
                It's hard to fully understand the details down to data level<br>
                It's challenge to keep update the latest best practice<br>
                Impossible triangle of scope-time-quality
              </td>
            </tr>
            <tr>
              <td>QA</td>
              <td>
                Test case design<br>
                Test execution<br>
                &bull; Manual: none<br>
                &bull; Auto: test automation technologies<br>
                Triage issues found during testing<br>
                Raise defect
              </td>
              <td>
                <span style="color: #388e3c;">Understand the functional spec <span style="color: #d32f2f;">down to data details</span></span><br>
                <span style="color: #388e3c;">Understand the technical solution <span style="color: #d32f2f;">down to data details</span></span><br>
                <span style="color: #388e3c;">Understand the development best practice</span>
              </td>
              <td>
                Time consuming if manual<br>
                Test data setup time consuming<br>
                Testing environment is flaky<br>
                It's hard to fully understand the details down to data level
              </td>
            </tr>
          </tbody>
        </table>
        </div>
      </section>
      <section class="slide section-content" id="section2-4">
        <h3>Communication Overheads</h3>
        <ul>
          <li>Many roles, each with specialized focus, leading to complex communication networks and overhead.</li>
          <li>Visuals (not included here) illustrate the communication complexity.</li>
        </ul>
        <img src="images/current-communication-net.png" alt="Current Communication Network" style="max-width: 200%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section3">
        <h2>Agile Squad Model (2010s)</h2>
        <p>
          In recent years, the convergence of cutting-edge technologies and agile methodologies has given rise to a new, streamlined working model in the tech industry. This model optimizes roles and responsibilities, leveraging technological advancements to enhance efficiency and reduce communication overhead.
        </p>
      </section>
      <section class="slide section-content" id="section3-1">
        <h3>Key Changes</h3>
        <ol>
          <li><b>Role Consolidation:</b>
            <ul>
              <li>Business Analyst and Project Manager merged into Product Manager.</li>
              <li>Solution Designer and Software Engineer merged into Solution Engineer.</li>
            </ul>
          </li>
          <li><b>Emphasis on Feature Breakdown:</b> Success relies on effective feature breakdown, simplifying development.</li>
          <li><b>Reduced Documentation and Communication Overhead:</b> Advanced technologies allow individuals to handle broader tasks, reducing role specialization and documentation.</li>
        </ol>
      </section>
      <section class="slide section-content" id="section3-2">
        <h3>Benefits of the Agile Model</h3>
        <ul>
          <li>Increased efficiency through role consolidation.</li>
          <li>Enhanced agility in project execution.</li>
          <li>Improved direct communication among team members.</li>
          <li>Better utilization of individual skills and capabilities.</li>
        </ul>
      </section>
      <section class="slide section-content" id="section3-3">
        <h3>Roles, Responsibilities, and Challenges in Agile</h3>
        <div style="overflow-x:auto;">
        <table class="roles-table">
          <thead>
            <tr>
              <th>Role</th>
              <th>Responsibility</th>
              <th>Knowledge required</th>
              <th>Challenge</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Product owner</td>
              <td>Make final decision of the product and delivery time</td>
              <td>
                <span style="color: #388e3c;">Understand the business and the value</span>
              </td>
              <td>
                Impossible triangle: scope-time-quality
              </td>
            </tr>
            <tr>
              <td>Product manager</td>
              <td>
                Define the product (requirement)<br>
                &bull; Write functional spec<br>
                &bull; Write non-functional spec<br>
                &bull; Feature break down<br>
                Manage epics<br>
                Communicate the functional spec to SE and QA<br>
                Monitor the project progress and status<br>
                Report to stakeholders<br>
                Coordinate any communication required<br>
                Release planning
              </td>
              <td>
                <span style="color: #388e3c;">Understand the business and the value</span><br>
                Understand the requirement on <span style="color: #d32f2f;">detail level <span style="color: #d32f2f;">down to data details</span></span><br>
                <span style="color: #388e3c;">Understand the technologies picture in the organization</span><br>
                <span style="color: #388e3c;">Understand the technology governance</span><br>
                <span style="color: #f57c00;">Understand the technical break down (from SE)</span><br>
                <span style="color: #d32f2f;">Understand the information needed by each audience</span>
              </td>
              <td>
                It's hard to fully understand the details down to data level<br>
                They all have different angle of focus on the functional spec. Hard to communicate well to each of them.
              </td>
            </tr>
            <tr>
              <td>Solution engineer</td>
              <td>
                Design the technical solution<br>
                &bull; Technical components break down<br>
                &bull; API design (swagger)<br>
                &bull; microservice design<br>
                Development<br>
                &bull; Manage tickets<br>
                &bull; Feature codes<br>
                &bull; Unit test<br>
                Communicate the technical solution to QA and PM
              </td>
              <td>
                <span style="color: #388e3c;">Understand the business and the value</span><br>
                <span style="color: #388e3c;">Understand the technologies picture in the organization</span><br>
                <span style="color: #388e3c;">Understand the technology governance</span><br>
                <span style="color: #f57c00;">Understand the inbound/outbound <span style="color: #d32f2f;">data details</span></span><br>
                <span style="color: #388e3c;">Understand the feature break down from functional spec</span><br>
                <span style="color: #f57c00;">Understand the development technologies</span><br>
                <span style="color: #388e3c;">Understand the functional spec <span style="color: #d32f2f;">down to data details</span></span><br>
                <span style="color: #d32f2f;">Understand the latest best practice and principal</span><br>
                <span style="color: #388e3c;">Understand the information needed by QA</span>
              </td>
              <td>
                It's hard to fully understand the details down to data level<br>
                Engineer need to have the ability of design<br>
                It's challenge to keep update the latest best practice<br>
                Impossible triangle of scope-time-quality
              </td>
            </tr>
            <tr>
              <td>QA</td>
              <td>
                Test case design<br>
                Test execution<br>
                &bull; Test automation technologies<br>
                Triage issues found during testing<br>
                Raise defect
              </td>
              <td>
                <span style="color: #d32f2f;">Understand the functional spec <span style="color: #d32f2f;">down to data details</span></span><br>
                <span style="color: #d32f2f;">Understand the technical solution <span style="color: #d32f2f;">down to data details</span></span>
              </td>
              <td>
                Test data setup time consuming<br>
                Testing environment is flaky<br>
                It's hard to fully understand the details down to data level
              </td>
            </tr>
          </tbody>
        </table>
        </div>
      </section>
      <section class="slide section-content" id="section3-4">
        <h3>Communication Net</h3>
        <ul>
          <li>Fewer roles and more direct communication, reducing complexity compared to Waterfall.</li>
        </ul>
        <img src="images/contemporary-model-communication-net.png" alt="Contemporary Model Communication Network" style="max-width: 200%; height: auto; margin: 20px 0;">
      </section>
      <section class="slide section-content" id="section4">
        <h2>Conclusion</h2>
        <p>
          While the Waterfall model has merits in certain scenarios, Agile methodologies have proven to be more adaptable and effective in today’s fast-paced, ever-changing software development landscape. Agile’s iterative approach, emphasis on customer collaboration, and ability to respond quickly to change make it better suited to modern development needs.
        </p>
        <p>
          As the software industry continues to evolve, the principles of Agile remain relevant, forming the foundation for newer practices like DevOps and continuous delivery. This adaptability ensures that Agile-based approaches will continue to outperform traditional Waterfall methods in meeting the dynamic challenges of software development.
        </p>
      </section>
    </main>
  </div>
  <script>
    // Improved tree menu navigation logic with event delegation
    document.addEventListener('DOMContentLoaded', function() {
      const outline = document.getElementById('outline');
      const sections = document.querySelectorAll('.section-content');

      function showSection(sectionId) {
        sections.forEach(sec => sec.classList.remove('active'));
        const target = document.getElementById(sectionId);
        if (target) target.classList.add('active');
      }

      outline.addEventListener('click', function(e) {
        // Handle expand/collapse
        if (e.target.classList.contains('tree-toggle')) {
          const node = e.target.parentElement;
          node.classList.toggle('expanded');
          e.target.textContent = node.classList.contains('expanded') ? '▼' : '▶';
          return;
        }
        // Handle section switching
        const item = e.target.closest('.tree-node, .tree-leaf');
        if (item && item.hasAttribute('data-section')) {
          outline.querySelectorAll('.tree-node, .tree-leaf').forEach(n => n.classList.remove('active'));
          item.classList.add('active');
          showSection(item.getAttribute('data-section'));
        }
      });

      // Show only the first section by default
      showSection('section1');
    });
  </script>
</body>
</html>