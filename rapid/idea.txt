Propasl of RAPID development system

 
Problem statement 
High manual effort

Expensive to control the quality and standard
  Overly complex workflow
  Missing unit test
Long wait times due to human dependencies
  Waiting couple days is usual
No business value delivered in early stages
  Up to 2 months
Overheads of boilerplate work




So we will end up building something like a "Westpac's Gemini CLI", make it a command like mesh

That's bigger picture. We think big but start small. Don't need to build this into MVP. I think the "generate from scratch" is good enough as the MVP

rapid-cli could be another standalone product

After that we will have the product portfolio - rapid-design, rapid-build, rapid-cli and rapid-mcp

And RAPID become a Westpac software delivery methodology, including the Contemporary Software Development Model, best practice, guidelines, and a set of tools (rapid-design, rapid-build, rapid-cli and rapid-mcp etc. )\









I am trying to build a code generation eco system namedl RAPID. (RAG Agentic Programmer Interactive Development) 

This tool has:
- Web UI as portal. It provides 
   - Rapid Designer
   - Rapid API Initializr
   - Rapid UI Initializr
   - Knowledge manager
- VS Code plugin named Rapid-code. 

The Web UI Connect to a Restful backend API server. 

The backend API server and the Rapid-code connect to the RAG Azure AI Services. 

The Azure AI Services includes:

- Azure Open AI (GPT 4.0, ada-text-002)
- Azure AI Search
- Azure vault
- Azure AD
- Azure 
- Azure prompt Flow
- Azure Logic Apps
- Azure Data Factory
- Azure Functions
- Azure Blob Storage
- Azure key vault

The Azure AI Services will integrate with share point and confluence and bitbucket. 

Please prepare a diagram as above with all the necessary icons.
