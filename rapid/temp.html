<h2>Where are we now – scaffolding break down</h2>
<ul>
  <li><b>Current API Scaffolding Development Workflow – High Effort, Low Value</b>
    <ul>
      <li>Run mesh codegen – Scaffold generation (~30 mins)</li>
      <li>Prepare pom.xml – Mesh standard (~500+ lines, ~4+ hours)</li>
      <li>Run maven command <code>mvn clean install</code> – (~3 mins)</li>
      <li>Write core components and unit test – Controller, Service, Mapper, Client, DB Repo (~1-2 days)</li>
      <li>Check-in & wait for code review – Human dependency (2–3 days delay)</li>
      <li>Tech Lead Review – (~0.5 day)</li>
    </ul>
  </li>
  <li>Before any business logic is written, ~3 developer-days per API spent</li>
</ul>




<h2>Where are we going – RAPID Build Agent</h2>
<ul>
    <li>Automate the scaffold building</li>
    <li>Present the idea
      <ul>
        <li>Select step needed</li>
        <li>Write some prompt</li>
        <li>Write some business logic</li>
        <li>~30 minutes effort</li>
      </ul>
    </li>
    <li>Generate the code scaffold with test cases in 1 minute</li>
  </ul>


  <h2>Problem Statement</h2>
  <ul>
    <li>High manual effort</li>
    <li>Expensive to control the quality and standard
      <ul>
        <li>Overly complex workflow</li>
        <li>Missing unit test</li>
      </ul>
    </li>
    <li>Long wait times due to human dependencies
      <ul>
        <li>Waiting couple days is usual</li>
      </ul>
    </li>
    <li>No business value delivered in early stages
      <ul>
        <li>Up to 2 months</li>
      </ul>
    </li>
    <li>Overheads of boilerplate work</li>
  </ul>
</section>
