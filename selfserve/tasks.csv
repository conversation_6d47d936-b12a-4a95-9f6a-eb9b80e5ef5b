Epic / Use story	Component	UI Page	Entry Data	Outbound action/data	Questions
Design	Feature HLD				
Design	UI HLD				
Tech preparation	MFE name/Mesh component/Repo	N/A			
Tech preparation	Page ID				
Tech preparation	Bio Catch 	All pages			
Tech preparation	Forensic Logs	All pages	Page event		
Tech preparation	Context Handover	All pages			
Initiation	Check first time	NA			
Initiation	Get ctp profile	NA			
Initiation	Get card list	NA			
Initiation	Check eligibility	NA			
Not eligible	Not eligible page - Setup contact details	Not eligible page	Check eligibility API response no email/phone registered	Go to personal and contact details	Display message from BE or FE?
Not eligible	Not eligible page - Only cancel	Not eligible page	User not enrol and no eligible card	Only Cancel and return to Wlive	
Not eligible	Not eligible page - Setup contact details	Not eligible page	Email/Phone number has been registered by other CTP profile	Go to personal and contact details	email/phone check by which API/resource?
Not eligible	Not eligible page - Register for SMS code	Not eligible page	SAFI Not registered	Register SMS	SAFI check byi which API/resource?
Show profile	Profile page - name/email/phone	Profile page	"Not first time, profile details"		How to know it's first time or nor?
Show profile	Profile page - enroled cards 	Profile page	"Not first time, some cards enrolled, some cards eligible to enrol"	Remove card	How to know it's first time or nor?
Show profile	Profile page - add cards button	Profile page	"Not first time, some cards enrolled, some cards eligible to enrol"	Add card	How to know it's first time or nor?
Manage profile	Profile page - profile update message + button	Profile page	"Not first time, some cards enrolled, some cards eligible to enrol"	Update profile	
Manage profile	Profile page - profile delete message + button	Profile page	"Not first time, some cards enrolled, some cards eligible to enrol"	Delete profile	
					
					
Remove card	Remove card confirmation	Remove card page	The card selected	Remove successful	How to know it's first time or nor?
Remove card	Remove card page successful message	Remove card page	The card selected	Remove successful	How to know it's first time or nor?
Add card	Add card page	Add card page	Eligible not enroled cards	Enrol card / Successful	
Add card	Add card successful page	Add card successful page	Enrolment API successful response	back to Wlive 	Or Profile page? 
					
Onboarding page	Onboarding page	Onboarding page	First time + Profile not registered 	Get started -> Add card page / flow	How to know it's first time or nor?
Onboarding page	Welcome page	Welcome page	First time + Profile registered 	Click to pay profile	How to know it's first time or nor?
					
Common page	Error component	Error page	Any error in calling API	Return to Wlive	Return to Wlive?
					
SPA (SPICA)	SPA denpendencis	SPA (SPICA)			
